"""
Comprehensive retry and error handling system for Shopify sync operations.
Implements exponential backoff, rate limit handling, and dead letter queue management.
"""

import asyncio
import logging
import random
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable, Union
from enum import Enum

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_, or_

from core.config import get_settings
from core.services.queue_service import celery_service, TaskPriority
from modules.sync.models import (
    WebhookEventLog, WebhookEventStatus, SyncJob, SyncJobStatus, DeadLetterQueue
)

logger = logging.getLogger(__name__)
settings = get_settings()


class ErrorType(str, Enum):
    """Classification of error types for retry logic."""
    RATE_LIMIT = "rate_limit"
    NETWORK_ERROR = "network_error"
    TEMPORARY_ERROR = "temporary_error"
    PERMANENT_ERROR = "permanent_error"
    VALIDATION_ERROR = "validation_error"
    AUTHENTICATION_ERROR = "authentication_error"
    RESOURCE_NOT_FOUND = "resource_not_found"
    SHOPIFY_API_ERROR = "shopify_api_error"


class RetryStrategy(str, Enum):
    """Retry strategy types."""
    EXPONENTIAL_BACKOFF = "exponential_backoff"
    LINEAR_BACKOFF = "linear_backoff"
    FIXED_DELAY = "fixed_delay"
    IMMEDIATE = "immediate"


class RetryHandler:
    """
    Comprehensive retry handler for Shopify sync operations.
    Implements intelligent retry logic with exponential backoff and error classification.
    """

    def __init__(self):
        self.max_retries = {
            ErrorType.RATE_LIMIT: 5,
            ErrorType.NETWORK_ERROR: 3,
            ErrorType.TEMPORARY_ERROR: 3,
            ErrorType.PERMANENT_ERROR: 0,
            ErrorType.VALIDATION_ERROR: 0,
            ErrorType.AUTHENTICATION_ERROR: 1,
            ErrorType.RESOURCE_NOT_FOUND: 1,
            ErrorType.SHOPIFY_API_ERROR: 2
        }
        
        self.base_delay_seconds = {
            ErrorType.RATE_LIMIT: 60,      # Start with 1 minute for rate limits
            ErrorType.NETWORK_ERROR: 5,    # Start with 5 seconds for network
            ErrorType.TEMPORARY_ERROR: 10, # Start with 10 seconds for temp errors
            ErrorType.SHOPIFY_API_ERROR: 30 # Start with 30 seconds for API errors
        }
        
        self.max_delay_seconds = 3600  # Maximum 1 hour delay

    async def retry_with_backoff(
        self,
        operation: Callable,
        operation_args: tuple = (),
        operation_kwargs: dict = None,
        max_retries: Optional[int] = None,
        base_delay: Optional[float] = None,
        strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF,
        error_classifier: Optional[Callable] = None
    ) -> Any:
        """
        Execute operation with retry logic and exponential backoff.
        
        Args:
            operation: Async function to execute
            operation_args: Arguments for operation
            operation_kwargs: Keyword arguments for operation
            max_retries: Maximum retry attempts
            base_delay: Base delay in seconds
            strategy: Retry strategy
            error_classifier: Function to classify errors
            
        Returns:
            Operation result
            
        Raises:
            Exception: If all retries are exhausted
        """
        operation_kwargs = operation_kwargs or {}
        error_classifier = error_classifier or self._classify_error
        
        last_exception = None
        
        for attempt in range((max_retries or 3) + 1):
            try:
                if attempt > 0:
                    logger.info(f"Retry attempt {attempt} for operation {operation.__name__}")
                
                result = await operation(*operation_args, **operation_kwargs)
                
                if attempt > 0:
                    logger.info(f"Operation {operation.__name__} succeeded on attempt {attempt + 1}")
                
                return result
                
            except Exception as e:
                last_exception = e
                error_type = error_classifier(e)
                
                logger.warning(
                    f"Operation {operation.__name__} failed on attempt {attempt + 1}: "
                    f"{str(e)} (classified as {error_type.value})"
                )
                
                # Check if we should retry
                if not self._should_retry(error_type, attempt, max_retries):
                    logger.error(
                        f"Not retrying operation {operation.__name__} due to error type "
                        f"{error_type.value} or max attempts reached"
                    )
                    break
                
                # Calculate delay
                delay = self._calculate_delay(
                    error_type=error_type,
                    attempt=attempt,
                    base_delay=base_delay,
                    strategy=strategy
                )
                
                if delay > 0:
                    logger.info(f"Waiting {delay:.2f} seconds before retry")
                    await asyncio.sleep(delay)
        
        # All retries exhausted
        logger.error(f"All retries exhausted for operation {operation.__name__}")
        raise last_exception

    async def retry_webhook_processing(
        self,
        db: AsyncSession,
        webhook_event_id: int
    ) -> bool:
        """
        Retry webhook processing with intelligent backoff.
        
        Args:
            db: Database session
            webhook_event_id: Webhook event ID to retry
            
        Returns:
            True if retry was scheduled, False if max retries reached
        """
        # Get webhook event
        result = await db.execute(
            select(WebhookEventLog).where(WebhookEventLog.id == webhook_event_id)
        )
        webhook_event = result.scalar_one_or_none()
        
        if not webhook_event:
            logger.error(f"Webhook event {webhook_event_id} not found")
            return False
        
        # Check if we should retry
        max_attempts = self.max_retries.get(ErrorType.SHOPIFY_API_ERROR, 3)
        if webhook_event.processing_attempts >= max_attempts:
            logger.warning(
                f"Webhook event {webhook_event_id} has reached max retry attempts "
                f"({webhook_event.processing_attempts})"
            )
            
            # Move to dead letter queue
            await self._move_webhook_to_dlq(db, webhook_event)
            return False
        
        # Calculate delay for retry
        delay = self._calculate_delay(
            error_type=ErrorType.SHOPIFY_API_ERROR,
            attempt=webhook_event.processing_attempts,
            strategy=RetryStrategy.EXPONENTIAL_BACKOFF
        )
        
        # Schedule retry
        task_data = {
            "webhook_event_id": webhook_event_id,
            "topic": webhook_event.topic,
            "shop_domain": webhook_event.shop_domain,
            "payload": webhook_event.payload,
            "retry_attempt": webhook_event.processing_attempts + 1,
            "created_at": datetime.utcnow().isoformat()
        }
        
        task_id = celery_service.enqueue_webhook_processing(
            task_data=task_data,
            priority=TaskPriority.HIGH,
            countdown=int(delay)  # Delay in seconds
        )
        
        # Update webhook event
        webhook_event.status = WebhookEventStatus.RETRYING.value
        webhook_event.processing_attempts += 1
        webhook_event.celery_task_id = task_id
        await db.commit()
        
        logger.info(
            f"Scheduled webhook retry for event {webhook_event_id} "
            f"(attempt {webhook_event.processing_attempts}) in {delay:.2f} seconds"
        )
        
        return True

    async def retry_sync_job(
        self,
        db: AsyncSession,
        sync_job_id: int
    ) -> bool:
        """
        Retry failed sync job with backoff.
        
        Args:
            db: Database session
            sync_job_id: Sync job ID to retry
            
        Returns:
            True if retry was scheduled, False if max retries reached
        """
        # Get sync job
        result = await db.execute(
            select(SyncJob).where(SyncJob.id == sync_job_id)
        )
        sync_job = result.scalar_one_or_none()
        
        if not sync_job:
            logger.error(f"Sync job {sync_job_id} not found")
            return False
        
        # Check retry limits
        if sync_job.retry_count >= sync_job.max_retries:
            logger.warning(
                f"Sync job {sync_job_id} has reached max retries ({sync_job.retry_count})"
            )
            
            # Move to dead letter queue
            await self._move_sync_job_to_dlq(db, sync_job)
            return False
        
        # Calculate delay
        delay = self._calculate_delay(
            error_type=ErrorType.SHOPIFY_API_ERROR,
            attempt=sync_job.retry_count,
            strategy=RetryStrategy.EXPONENTIAL_BACKOFF
        )
        
        # Schedule retry
        task_data = {
            "store_id": sync_job.store_id,
            "entity_type": sync_job.entity_type,
            "sync_mode": sync_job.sync_mode,
            "batch_size": sync_job.batch_size,
            "retry_attempt": sync_job.retry_count + 1,
            "original_job_id": sync_job_id,
            "created_at": datetime.utcnow().isoformat()
        }
        
        task_id = celery_service.enqueue_sync_job(
            job_data=task_data,
            priority=TaskPriority.NORMAL,
            countdown=int(delay)
        )
        
        # Update sync job
        sync_job.status = SyncJobStatus.RETRYING.value
        sync_job.retry_count += 1
        sync_job.celery_task_id = task_id
        await db.commit()
        
        logger.info(
            f"Scheduled sync job retry for {sync_job_id} "
            f"(attempt {sync_job.retry_count}) in {delay:.2f} seconds"
        )
        
        return True

    def _classify_error(self, error: Exception) -> ErrorType:
        """
        Classify error type for appropriate retry strategy.
        
        Args:
            error: Exception to classify
            
        Returns:
            ErrorType classification
        """
        error_str = str(error).lower()
        
        # Rate limit errors
        if "rate limit" in error_str or "429" in error_str:
            return ErrorType.RATE_LIMIT
        
        # Authentication errors
        if "unauthorized" in error_str or "401" in error_str or "403" in error_str:
            return ErrorType.AUTHENTICATION_ERROR
        
        # Not found errors
        if "not found" in error_str or "404" in error_str:
            return ErrorType.RESOURCE_NOT_FOUND
        
        # Validation errors
        if "validation" in error_str or "400" in error_str or "422" in error_str:
            return ErrorType.VALIDATION_ERROR
        
        # Network errors
        if any(keyword in error_str for keyword in [
            "connection", "timeout", "network", "dns", "socket"
        ]):
            return ErrorType.NETWORK_ERROR
        
        # Shopify API errors
        if any(keyword in error_str for keyword in [
            "shopify", "graphql", "api", "500", "502", "503", "504"
        ]):
            return ErrorType.SHOPIFY_API_ERROR
        
        # Default to temporary error
        return ErrorType.TEMPORARY_ERROR

    def _should_retry(
        self,
        error_type: ErrorType,
        attempt: int,
        max_retries: Optional[int] = None
    ) -> bool:
        """
        Determine if operation should be retried.
        
        Args:
            error_type: Type of error
            attempt: Current attempt number (0-based)
            max_retries: Override max retries
            
        Returns:
            True if should retry, False otherwise
        """
        max_attempts = max_retries or self.max_retries.get(error_type, 0)
        return attempt < max_attempts

    def _calculate_delay(
        self,
        error_type: ErrorType,
        attempt: int,
        base_delay: Optional[float] = None,
        strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF
    ) -> float:
        """
        Calculate delay before retry based on strategy and error type.
        
        Args:
            error_type: Type of error
            attempt: Current attempt number (0-based)
            base_delay: Override base delay
            strategy: Retry strategy
            
        Returns:
            Delay in seconds
        """
        base = base_delay or self.base_delay_seconds.get(error_type, 10)
        
        if strategy == RetryStrategy.EXPONENTIAL_BACKOFF:
            # Exponential backoff with jitter
            delay = base * (2 ** attempt)
            jitter = random.uniform(0.1, 0.3) * delay
            delay += jitter
        elif strategy == RetryStrategy.LINEAR_BACKOFF:
            # Linear backoff
            delay = base * (attempt + 1)
        elif strategy == RetryStrategy.FIXED_DELAY:
            # Fixed delay
            delay = base
        else:  # IMMEDIATE
            delay = 0
        
        # Cap at maximum delay
        return min(delay, self.max_delay_seconds)

    async def _move_webhook_to_dlq(
        self,
        db: AsyncSession,
        webhook_event: WebhookEventLog
    ) -> None:
        """Move webhook event to dead letter queue."""
        dlq_item = DeadLetterQueue(
            source_type="webhook",
            source_id=str(webhook_event.id),
            entity_type=webhook_event.topic,
            failure_reason="max_retries_exceeded",
            error_message=webhook_event.last_error_message or "Unknown error",
            original_payload=webhook_event.payload,
            processing_history={
                "attempts": webhook_event.processing_attempts,
                "last_error": webhook_event.last_error_message,
                "event_id": webhook_event.event_id,
                "topic": webhook_event.topic,
                "shop_domain": webhook_event.shop_domain
            }
        )
        
        db.add(dlq_item)
        
        # Update webhook event status
        webhook_event.status = WebhookEventStatus.FAILED.value
        
        await db.commit()
        
        logger.error(
            f"Moved webhook event {webhook_event.id} to dead letter queue "
            f"after {webhook_event.processing_attempts} attempts"
        )

    async def _move_sync_job_to_dlq(
        self,
        db: AsyncSession,
        sync_job: SyncJob
    ) -> None:
        """Move sync job to dead letter queue."""
        dlq_item = DeadLetterQueue(
            source_type="sync_job",
            source_id=str(sync_job.id),
            entity_type=sync_job.entity_type,
            store_id=sync_job.store_id,
            failure_reason="max_retries_exceeded",
            error_message=sync_job.last_error_message or "Unknown error",
            original_payload={
                "job_type": sync_job.job_type,
                "sync_mode": sync_job.sync_mode,
                "batch_size": sync_job.batch_size,
                "total_entities": sync_job.total_entities,
                "processed_entities": sync_job.processed_entities
            },
            processing_history={
                "retry_count": sync_job.retry_count,
                "last_error": sync_job.last_error_message,
                "duration_seconds": sync_job.duration_seconds,
                "api_calls_made": sync_job.api_calls_made
            }
        )
        
        db.add(dlq_item)
        
        # Update sync job status
        sync_job.status = SyncJobStatus.FAILED.value
        
        await db.commit()
        
        logger.error(
            f"Moved sync job {sync_job.id} to dead letter queue "
            f"after {sync_job.retry_count} retries"
        )


# Global retry handler instance
retry_handler = RetryHandler()
