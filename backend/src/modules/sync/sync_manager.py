"""
Advanced Shopify sync manager with GraphQL Admin API, checkpointing, and incremental sync.
Implements Shopify best practices for data synchronization with rate limiting and error handling.
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Tu<PERSON>, AsyncGenerator
from enum import Enum

import httpx
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_, or_

from core.config import get_settings
from modules.sync.models import (
    Sync<PERSON>heckpoint, SyncJob, SyncJobStatus, SyncEntityType, DeadLetterQueue
)
from modules.stores.models import Store
from plugins.shopify.shopify_service import ShopifyGraphQLService

logger = logging.getLogger(__name__)
settings = get_settings()


class SyncMode(str, Enum):
    """Sync operation modes."""
    INCREMENTAL = "incremental"
    FULL = "full"
    BACKFILL = "backfill"
    BULK = "bulk"


class RateLimitStrategy(str, Enum):
    """Rate limiting strategies."""
    CONSERVATIVE = "conservative"  # 40% of limit
    MODERATE = "moderate"         # 60% of limit
    AGGRESSIVE = "aggressive"     # 80% of limit


class SyncManager:
    """
    Advanced sync manager for Shopify data with GraphQL Admin API.
    Implements incremental sync with checkpointing, rate limiting, and error handling.
    """

    def __init__(
        self,
        store: Store,
        rate_limit_strategy: RateLimitStrategy = RateLimitStrategy.MODERATE
    ):
        self.store = store
        self.rate_limit_strategy = rate_limit_strategy
        self.shopify_service = ShopifyGraphQLService(
            shop_domain=store.shop_domain,
            admin_access_token=store.admin_access_token,
            storefront_access_token=store.storefront_access_token
        )
        
        # Rate limiting configuration
        self.rate_limits = {
            RateLimitStrategy.CONSERVATIVE: 0.4,
            RateLimitStrategy.MODERATE: 0.6,
            RateLimitStrategy.AGGRESSIVE: 0.8
        }
        
        # GraphQL cost limits
        self.max_query_cost = 1000  # Shopify's single query limit
        self.cost_threshold = self.max_query_cost * self.rate_limits[rate_limit_strategy]

    async def sync_entity(
        self,
        db: AsyncSession,
        entity_type: SyncEntityType,
        sync_mode: SyncMode = SyncMode.INCREMENTAL,
        batch_size: int = 50,
        max_pages: Optional[int] = None
    ) -> SyncJob:
        """
        Main entry point for entity synchronization.
        
        Args:
            db: Database session
            entity_type: Type of entity to sync
            sync_mode: Sync mode (incremental, full, etc.)
            batch_size: Number of items per batch
            max_pages: Maximum pages to process (for testing)
            
        Returns:
            SyncJob instance with results
        """
        logger.info(f"Starting {sync_mode.value} sync for {entity_type.value}")
        
        # Create sync job
        sync_job = await self._create_sync_job(
            db=db,
            entity_type=entity_type,
            sync_mode=sync_mode,
            batch_size=batch_size
        )
        
        try:
            # Get or create checkpoint
            checkpoint = await self._get_or_create_checkpoint(db, entity_type)
            
            # Determine sync strategy
            if sync_mode == SyncMode.INCREMENTAL:
                await self._sync_incremental(
                    db=db,
                    sync_job=sync_job,
                    checkpoint=checkpoint,
                    entity_type=entity_type,
                    batch_size=batch_size,
                    max_pages=max_pages
                )
            elif sync_mode == SyncMode.FULL:
                await self._sync_full(
                    db=db,
                    sync_job=sync_job,
                    checkpoint=checkpoint,
                    entity_type=entity_type,
                    batch_size=batch_size,
                    max_pages=max_pages
                )
            elif sync_mode == SyncMode.BULK:
                await self._sync_bulk(
                    db=db,
                    sync_job=sync_job,
                    checkpoint=checkpoint,
                    entity_type=entity_type
                )
            
            # Mark job as completed
            await self._complete_sync_job(db, sync_job, "completed")
            
        except Exception as e:
            logger.error(f"Sync job {sync_job.id} failed: {str(e)}", exc_info=True)
            await self._complete_sync_job(db, sync_job, "failed", str(e))
            raise
        
        return sync_job

    async def _sync_incremental(
        self,
        db: AsyncSession,
        sync_job: SyncJob,
        checkpoint: SyncCheckpoint,
        entity_type: SyncEntityType,
        batch_size: int,
        max_pages: Optional[int] = None
    ) -> None:
        """
        Perform incremental sync using checkpoints and updated_at filtering.
        
        Args:
            db: Database session
            sync_job: Sync job instance
            checkpoint: Sync checkpoint
            entity_type: Entity type to sync
            batch_size: Batch size
            max_pages: Maximum pages to process
        """
        logger.info(f"Starting incremental sync from checkpoint: {checkpoint.last_updated_at}")
        
        # Build GraphQL query with updated_at filter
        since_date = checkpoint.last_updated_at or datetime(2020, 1, 1)
        query_filter = f"updated_at:>={since_date.isoformat()}"
        
        # Process pages with cursor-based pagination
        cursor = checkpoint.last_end_cursor
        page_count = 0
        total_processed = 0
        
        async for page_data in self._paginate_entities(
            entity_type=entity_type,
            query_filter=query_filter,
            batch_size=batch_size,
            start_cursor=cursor
        ):
            if max_pages and page_count >= max_pages:
                break
                
            page_count += 1
            
            # Process page
            processed_count = await self._process_page(
                db=db,
                sync_job=sync_job,
                page_data=page_data,
                entity_type=entity_type
            )
            
            total_processed += processed_count
            
            # Update checkpoint after each page (atomic commit)
            await self._update_checkpoint_atomic(
                db=db,
                checkpoint=checkpoint,
                page_data=page_data,
                processed_count=processed_count
            )
            
            # Update sync job progress
            await self._update_sync_job_progress(
                db=db,
                sync_job=sync_job,
                processed_entities=total_processed,
                current_page=page_count
            )
            
            # Rate limiting check
            await self._check_rate_limits()
            
            logger.info(f"Processed page {page_count}, {processed_count} entities")

    async def _sync_full(
        self,
        db: AsyncSession,
        sync_job: SyncJob,
        checkpoint: SyncCheckpoint,
        entity_type: SyncEntityType,
        batch_size: int,
        max_pages: Optional[int] = None
    ) -> None:
        """
        Perform full sync of all entities.
        
        Args:
            db: Database session
            sync_job: Sync job instance
            checkpoint: Sync checkpoint
            entity_type: Entity type to sync
            batch_size: Batch size
            max_pages: Maximum pages to process
        """
        logger.info("Starting full sync")
        
        # Reset checkpoint for full sync
        checkpoint.last_updated_at = None
        checkpoint.last_synced_id = None
        checkpoint.last_end_cursor = None
        await db.commit()
        
        # Process all entities with cursor pagination
        page_count = 0
        total_processed = 0
        
        async for page_data in self._paginate_entities(
            entity_type=entity_type,
            query_filter=None,
            batch_size=batch_size,
            start_cursor=None
        ):
            if max_pages and page_count >= max_pages:
                break
                
            page_count += 1
            
            # Process page
            processed_count = await self._process_page(
                db=db,
                sync_job=sync_job,
                page_data=page_data,
                entity_type=entity_type
            )
            
            total_processed += processed_count
            
            # Update checkpoint
            await self._update_checkpoint_atomic(
                db=db,
                checkpoint=checkpoint,
                page_data=page_data,
                processed_count=processed_count
            )
            
            # Update sync job progress
            await self._update_sync_job_progress(
                db=db,
                sync_job=sync_job,
                processed_entities=total_processed,
                current_page=page_count
            )
            
            # Rate limiting
            await self._check_rate_limits()

    async def _sync_bulk(
        self,
        db: AsyncSession,
        sync_job: SyncJob,
        checkpoint: SyncCheckpoint,
        entity_type: SyncEntityType
    ) -> None:
        """
        Perform bulk sync using Shopify's Bulk Operations API.
        
        Args:
            db: Database session
            sync_job: Sync job instance
            checkpoint: Sync checkpoint
            entity_type: Entity type to sync
        """
        logger.info("Starting bulk sync operation")
        
        # Create bulk operation query
        bulk_query = self._build_bulk_query(entity_type)
        
        # Start bulk operation
        bulk_operation = await self.shopify_service.start_bulk_operation(bulk_query)
        
        if not bulk_operation:
            raise Exception("Failed to start bulk operation")
        
        # Poll for completion
        operation_id = bulk_operation.get("id")
        await self._poll_bulk_operation(db, sync_job, operation_id)
        
        # Download and process results
        download_url = await self.shopify_service.get_bulk_operation_result(operation_id)
        if download_url:
            await self._process_bulk_results(db, sync_job, download_url, entity_type)

    async def _paginate_entities(
        self,
        entity_type: SyncEntityType,
        query_filter: Optional[str],
        batch_size: int,
        start_cursor: Optional[str] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Paginate through entities using GraphQL cursor-based pagination.
        
        Args:
            entity_type: Type of entity to paginate
            query_filter: GraphQL query filter
            batch_size: Number of items per page
            start_cursor: Starting cursor for pagination
            
        Yields:
            Page data with entities and pagination info
        """
        cursor = start_cursor
        has_next_page = True
        
        while has_next_page:
            # Build GraphQL query
            query = self._build_pagination_query(
                entity_type=entity_type,
                batch_size=batch_size,
                cursor=cursor,
                query_filter=query_filter
            )
            
            # Execute query
            result = await self.shopify_service._execute_admin_query(query)
            
            if "errors" in result:
                logger.error(f"GraphQL errors: {result['errors']}")
                break
            
            # Extract page data
            data = result.get("data", {})
            entity_key = self._get_entity_key(entity_type)
            entity_data = data.get(entity_key, {})
            
            edges = entity_data.get("edges", [])
            page_info = entity_data.get("pageInfo", {})
            
            if not edges:
                break
            
            yield {
                "edges": edges,
                "pageInfo": page_info,
                "totalCount": entity_data.get("totalCount", 0)
            }
            
            # Update cursor for next page
            has_next_page = page_info.get("hasNextPage", False)
            cursor = page_info.get("endCursor") if has_next_page else None
            
            # Rate limiting
            await self._check_rate_limits()

    def _build_pagination_query(
        self,
        entity_type: SyncEntityType,
        batch_size: int,
        cursor: Optional[str],
        query_filter: Optional[str]
    ) -> str:
        """
        Build GraphQL pagination query for entity type.
        
        Args:
            entity_type: Entity type
            batch_size: Batch size
            cursor: Pagination cursor
            query_filter: Query filter
            
        Returns:
            GraphQL query string
        """
        entity_key = self._get_entity_key(entity_type)
        fields = self._get_entity_fields(entity_type)
        
        # Build arguments
        args = [f"first: {batch_size}"]
        if cursor:
            args.append(f'after: "{cursor}"')
        if query_filter:
            args.append(f'query: "{query_filter}"')
        args.append('sortKey: UPDATED_AT')
        
        args_str = ", ".join(args)
        
        return f"""
        query {{
            {entity_key}({args_str}) {{
                totalCount
                edges {{
                    cursor
                    node {{
                        {fields}
                    }}
                }}
                pageInfo {{
                    hasNextPage
                    hasPreviousPage
                    startCursor
                    endCursor
                }}
            }}
        }}
        """

    def _get_entity_key(self, entity_type: SyncEntityType) -> str:
        """Get GraphQL entity key for entity type."""
        mapping = {
            SyncEntityType.PRODUCTS: "products",
            SyncEntityType.ORDERS: "orders",
            SyncEntityType.CUSTOMERS: "customers",
            SyncEntityType.COLLECTIONS: "collections"
        }
        return mapping.get(entity_type, "products")

    def _get_entity_fields(self, entity_type: SyncEntityType) -> str:
        """Get GraphQL fields for entity type."""
        if entity_type == SyncEntityType.PRODUCTS:
            return """
                id
                legacyResourceId
                title
                description
                handle
                status
                vendor
                productType
                tags
                createdAt
                updatedAt
                publishedAt
                totalInventory
                totalVariants
                hasOnlyDefaultVariant
                tracksInventory
                onlineStoreUrl
                featuredImage {
                    url
                    altText
                }
                images(first: 10) {
                    edges {
                        node {
                            url
                            altText
                        }
                    }
                }
                variants(first: 100) {
                    edges {
                        node {
                            id
                            legacyResourceId
                            title
                            price
                            compareAtPrice
                            sku
                            barcode
                            inventoryQuantity
                            weight
                            weightUnit
                            requiresShipping
                            taxable
                            inventoryPolicy
                            inventoryManagement
                            createdAt
                            updatedAt
                        }
                    }
                }
            """
        elif entity_type == SyncEntityType.ORDERS:
            return """
                id
                legacyResourceId
                name
                email
                phone
                createdAt
                updatedAt
                processedAt
                closedAt
                cancelledAt
                totalPrice
                subtotalPrice
                totalTax
                totalDiscounts
                currencyCode
                financialStatus
                fulfillmentStatus
                confirmed
                test
                tags
                note
                customer {
                    id
                    legacyResourceId
                    email
                    firstName
                    lastName
                }
                lineItems(first: 100) {
                    edges {
                        node {
                            id
                            title
                            quantity
                            originalUnitPrice
                            discountedUnitPrice
                            variant {
                                id
                                legacyResourceId
                                sku
                            }
                            product {
                                id
                                legacyResourceId
                            }
                        }
                    }
                }
            """
        elif entity_type == SyncEntityType.CUSTOMERS:
            return """
                id
                legacyResourceId
                email
                firstName
                lastName
                phone
                acceptsMarketing
                taxExempt
                verifiedEmail
                state
                note
                tags
                ordersCount
                totalSpent
                createdAt
                updatedAt
                addresses(first: 10) {
                    id
                    firstName
                    lastName
                    company
                    address1
                    address2
                    city
                    province
                    zip
                    country
                    phone
                }
            """
        else:
            return "id legacyResourceId createdAt updatedAt"

    async def _process_page(
        self,
        db: AsyncSession,
        sync_job: SyncJob,
        page_data: Dict[str, Any],
        entity_type: SyncEntityType
    ) -> int:
        """
        Process a page of entities and sync to database.

        Args:
            db: Database session
            sync_job: Sync job instance
            page_data: Page data with entities
            entity_type: Entity type

        Returns:
            Number of entities processed
        """
        edges = page_data.get("edges", [])
        processed_count = 0

        # Process each entity in the page
        for edge in edges:
            entity_data = edge["node"]

            try:
                # Sync entity to database
                await self._sync_entity_to_db(db, entity_data, entity_type)
                processed_count += 1
                sync_job.successful_entities += 1

            except Exception as e:
                logger.error(f"Failed to sync entity {entity_data.get('id')}: {str(e)}")
                sync_job.failed_entities += 1

                # Add to dead letter queue if critical error
                await self._add_to_dead_letter_queue(
                    db=db,
                    source_type="sync_job",
                    source_id=str(sync_job.id),
                    entity_type=entity_type.value,
                    error_message=str(e),
                    payload=entity_data
                )

        # Update API usage metrics
        sync_job.api_calls_made += 1
        await db.commit()

        return processed_count

    async def _sync_entity_to_db(
        self,
        db: AsyncSession,
        entity_data: Dict[str, Any],
        entity_type: SyncEntityType
    ) -> None:
        """
        Sync individual entity to database.

        Args:
            db: Database session
            entity_data: Entity data from GraphQL
            entity_type: Entity type
        """
        # Import sync services
        from modules.stores.sync_service import DBService

        db_service = DBService(self.store.shop_domain)

        if entity_type == SyncEntityType.PRODUCTS:
            await db_service.sync_product_to_db(db, entity_data)
        elif entity_type == SyncEntityType.ORDERS:
            await db_service.sync_order_to_db(db, entity_data)
        elif entity_type == SyncEntityType.CUSTOMERS:
            await db_service.sync_customer_to_db(db, entity_data)
        else:
            logger.warning(f"Unsupported entity type for sync: {entity_type}")

    async def _create_sync_job(
        self,
        db: AsyncSession,
        entity_type: SyncEntityType,
        sync_mode: SyncMode,
        batch_size: int
    ) -> SyncJob:
        """Create new sync job record."""
        sync_job = SyncJob(
            store_id=self.store.id,
            entity_type=entity_type.value,
            job_type=sync_mode.value,
            sync_mode=sync_mode.value,
            batch_size=batch_size,
            status=SyncJobStatus.RUNNING.value,
            started_at=datetime.utcnow()
        )

        db.add(sync_job)
        await db.commit()
        await db.refresh(sync_job)

        return sync_job

    async def _get_or_create_checkpoint(
        self,
        db: AsyncSession,
        entity_type: SyncEntityType
    ) -> SyncCheckpoint:
        """Get existing checkpoint or create new one."""
        result = await db.execute(
            select(SyncCheckpoint).where(
                and_(
                    SyncCheckpoint.store_id == self.store.id,
                    SyncCheckpoint.entity_type == entity_type.value
                )
            )
        )

        checkpoint = result.scalar_one_or_none()

        if not checkpoint:
            checkpoint = SyncCheckpoint(
                store_id=self.store.id,
                entity_type=entity_type.value,
                last_sync_status="pending"
            )
            db.add(checkpoint)
            await db.commit()
            await db.refresh(checkpoint)

        return checkpoint

    async def _update_checkpoint_atomic(
        self,
        db: AsyncSession,
        checkpoint: SyncCheckpoint,
        page_data: Dict[str, Any],
        processed_count: int
    ) -> None:
        """Update checkpoint atomically after processing page."""
        edges = page_data.get("edges", [])
        page_info = page_data.get("pageInfo", {})

        if edges:
            # Get last entity's updated_at timestamp
            last_entity = edges[-1]["node"]
            last_updated_at = last_entity.get("updatedAt")
            last_id = last_entity.get("legacyResourceId") or last_entity.get("id")

            # Update checkpoint
            checkpoint.last_updated_at = datetime.fromisoformat(
                last_updated_at.replace('Z', '+00:00')
            ) if last_updated_at else None
            checkpoint.last_synced_id = str(last_id) if last_id else None
            checkpoint.last_end_cursor = page_info.get("endCursor")
            checkpoint.last_sync_status = "running"
            checkpoint.updated_at = datetime.utcnow()

            await db.commit()

    async def _update_sync_job_progress(
        self,
        db: AsyncSession,
        sync_job: SyncJob,
        processed_entities: int,
        current_page: int
    ) -> None:
        """Update sync job progress."""
        sync_job.processed_entities = processed_entities
        sync_job.current_page = current_page

        # Calculate ETA
        if sync_job.total_entities > 0:
            eta_seconds = sync_job.calculate_eta_seconds()
            sync_job.estimated_completion_at = datetime.utcnow() + timedelta(seconds=eta_seconds)

        await db.commit()

    async def _complete_sync_job(
        self,
        db: AsyncSession,
        sync_job: SyncJob,
        status: str,
        error_message: Optional[str] = None
    ) -> None:
        """Complete sync job with final status."""
        sync_job.status = status
        sync_job.completed_at = datetime.utcnow()

        if sync_job.started_at:
            sync_job.duration_seconds = (
                sync_job.completed_at - sync_job.started_at
            ).total_seconds()

        if error_message:
            sync_job.last_error_message = error_message

        await db.commit()

    async def _check_rate_limits(self) -> None:
        """Check and respect Shopify API rate limits."""
        # Get current rate limit status from last API call
        rate_limit_info = getattr(self.shopify_service, '_last_rate_limit_info', None)

        if rate_limit_info:
            current_cost = rate_limit_info.get('currentlyAvailable', 1000)
            max_cost = rate_limit_info.get('maximumAvailable', 1000)
            restore_rate = rate_limit_info.get('restoreRate', 50)

            # Calculate usage percentage
            usage_percentage = (max_cost - current_cost) / max_cost

            # If we're approaching the limit, wait
            if usage_percentage > self.rate_limits[self.rate_limit_strategy]:
                wait_time = (max_cost - current_cost) / restore_rate
                logger.info(f"Rate limit threshold reached, waiting {wait_time:.2f} seconds")
                await asyncio.sleep(wait_time)

    async def _add_to_dead_letter_queue(
        self,
        db: AsyncSession,
        source_type: str,
        source_id: str,
        entity_type: str,
        error_message: str,
        payload: Dict[str, Any]
    ) -> None:
        """Add failed item to dead letter queue."""
        dlq_item = DeadLetterQueue(
            source_type=source_type,
            source_id=source_id,
            entity_type=entity_type,
            store_id=self.store.id,
            failure_reason="processing_error",
            error_message=error_message,
            original_payload=payload
        )

        db.add(dlq_item)
        await db.commit()

    def _build_bulk_query(self, entity_type: SyncEntityType) -> str:
        """Build bulk operation query for entity type."""
        entity_key = self._get_entity_key(entity_type)
        fields = self._get_entity_fields(entity_type)

        return f"""
        {{
            {entity_key} {{
                edges {{
                    node {{
                        {fields}
                    }}
                }}
            }}
        }}
        """

    async def _poll_bulk_operation(
        self,
        db: AsyncSession,
        sync_job: SyncJob,
        operation_id: str
    ) -> None:
        """Poll bulk operation until completion."""
        max_wait_time = 3600  # 1 hour max
        poll_interval = 30    # 30 seconds
        elapsed_time = 0

        while elapsed_time < max_wait_time:
            status = await self.shopify_service.get_bulk_operation_status(operation_id)

            if status == "COMPLETED":
                logger.info(f"Bulk operation {operation_id} completed")
                return
            elif status == "FAILED":
                raise Exception(f"Bulk operation {operation_id} failed")

            await asyncio.sleep(poll_interval)
            elapsed_time += poll_interval

        raise Exception(f"Bulk operation {operation_id} timed out")

    async def _process_bulk_results(
        self,
        db: AsyncSession,
        sync_job: SyncJob,
        download_url: str,
        entity_type: SyncEntityType
    ) -> None:
        """Process bulk operation results."""
        # Download and process JSONL file
        async with httpx.AsyncClient() as client:
            response = await client.get(download_url)
            response.raise_for_status()

            lines = response.text.strip().split('\n')

            for line in lines:
                if line.strip():
                    entity_data = json.loads(line)
                    await self._sync_entity_to_db(db, entity_data, entity_type)
