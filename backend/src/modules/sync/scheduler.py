"""
Scheduled sync service for Shopify data synchronization.
Implements backstop sync jobs and reconciliation processes.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from enum import Enum

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_

from core.config import get_settings
from core.services.queue_service import celery_service, TaskPriority
from modules.sync.models import SyncEntityType, SyncMode, SyncCheckpoint
from modules.stores.models import Store

logger = logging.getLogger(__name__)
settings = get_settings()


class ScheduleFrequency(str, Enum):
    """Schedule frequency options."""
    EVERY_5_MINUTES = "*/5 * * * *"
    EVERY_15_MINUTES = "*/15 * * * *"
    EVERY_30_MINUTES = "*/30 * * * *"
    HOURLY = "0 * * * *"
    EVERY_2_HOURS = "0 */2 * * *"
    EVERY_6_HOURS = "0 */6 * * *"
    DAILY = "0 0 * * *"
    WEEKLY = "0 0 * * 0"


class SyncScheduler:
    """
    Scheduled sync service for backstop synchronization and reconciliation.
    Ensures data consistency even when webhooks fail or are missed.
    """

    def __init__(self):
        # Default sync schedules per entity type
        self.default_schedules = {
            SyncEntityType.PRODUCTS: ScheduleFrequency.EVERY_30_MINUTES,
            SyncEntityType.ORDERS: ScheduleFrequency.EVERY_15_MINUTES,
            SyncEntityType.CUSTOMERS: ScheduleFrequency.HOURLY,
            SyncEntityType.COLLECTIONS: ScheduleFrequency.EVERY_2_HOURS,
            SyncEntityType.INVENTORY: ScheduleFrequency.EVERY_5_MINUTES
        }
        
        # Reconciliation schedules (full sync)
        self.reconciliation_schedules = {
            SyncEntityType.PRODUCTS: ScheduleFrequency.DAILY,
            SyncEntityType.ORDERS: ScheduleFrequency.DAILY,
            SyncEntityType.CUSTOMERS: ScheduleFrequency.WEEKLY,
            SyncEntityType.COLLECTIONS: ScheduleFrequency.WEEKLY,
            SyncEntityType.INVENTORY: ScheduleFrequency.DAILY
        }

    async def schedule_incremental_syncs(
        self,
        db: AsyncSession,
        store_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Schedule incremental sync jobs for all active stores.
        
        Args:
            db: Database session
            store_id: Optional specific store ID to sync
            
        Returns:
            Summary of scheduled jobs
        """
        logger.info("Starting scheduled incremental sync process")
        
        # Get active stores
        stores = await self._get_active_stores(db, store_id)
        
        scheduled_jobs = []
        
        for store in stores:
            for entity_type in SyncEntityType:
                # Check if sync is needed
                should_sync = await self._should_schedule_sync(
                    db=db,
                    store=store,
                    entity_type=entity_type,
                    sync_mode=SyncMode.INCREMENTAL
                )
                
                if should_sync:
                    # Schedule incremental sync
                    task_id = await self._schedule_sync_job(
                        store=store,
                        entity_type=entity_type,
                        sync_mode=SyncMode.INCREMENTAL,
                        priority=TaskPriority.NORMAL
                    )
                    
                    scheduled_jobs.append({
                        "store_id": store.id,
                        "shop_domain": store.shop_domain,
                        "entity_type": entity_type.value,
                        "sync_mode": SyncMode.INCREMENTAL.value,
                        "task_id": task_id
                    })
                    
                    logger.info(
                        f"Scheduled incremental {entity_type.value} sync for store {store.id} "
                        f"({store.shop_domain})"
                    )
        
        return {
            "scheduled_jobs": len(scheduled_jobs),
            "jobs": scheduled_jobs,
            "timestamp": datetime.utcnow().isoformat()
        }

    async def schedule_reconciliation_syncs(
        self,
        db: AsyncSession,
        store_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Schedule full reconciliation sync jobs.
        
        Args:
            db: Database session
            store_id: Optional specific store ID to sync
            
        Returns:
            Summary of scheduled reconciliation jobs
        """
        logger.info("Starting scheduled reconciliation sync process")
        
        # Get active stores
        stores = await self._get_active_stores(db, store_id)
        
        scheduled_jobs = []
        
        for store in stores:
            for entity_type in SyncEntityType:
                # Check if reconciliation is needed
                should_reconcile = await self._should_schedule_reconciliation(
                    db=db,
                    store=store,
                    entity_type=entity_type
                )
                
                if should_reconcile:
                    # Schedule full sync for reconciliation
                    task_id = await self._schedule_sync_job(
                        store=store,
                        entity_type=entity_type,
                        sync_mode=SyncMode.FULL,
                        priority=TaskPriority.LOW  # Lower priority for reconciliation
                    )
                    
                    scheduled_jobs.append({
                        "store_id": store.id,
                        "shop_domain": store.shop_domain,
                        "entity_type": entity_type.value,
                        "sync_mode": SyncMode.FULL.value,
                        "task_id": task_id
                    })
                    
                    logger.info(
                        f"Scheduled reconciliation {entity_type.value} sync for store {store.id} "
                        f"({store.shop_domain})"
                    )
        
        return {
            "scheduled_jobs": len(scheduled_jobs),
            "jobs": scheduled_jobs,
            "timestamp": datetime.utcnow().isoformat()
        }

    async def _get_active_stores(
        self,
        db: AsyncSession,
        store_id: Optional[int] = None
    ) -> List[Store]:
        """Get active stores for sync scheduling."""
        filters = [Store.is_active == True]
        
        if store_id:
            filters.append(Store.id == store_id)
        
        result = await db.execute(
            select(Store).where(and_(*filters))
        )
        
        return result.scalars().all()

    async def _should_schedule_sync(
        self,
        db: AsyncSession,
        store: Store,
        entity_type: SyncEntityType,
        sync_mode: SyncMode
    ) -> bool:
        """
        Determine if a sync should be scheduled based on last sync time and frequency.
        
        Args:
            db: Database session
            store: Store instance
            entity_type: Entity type to check
            sync_mode: Sync mode
            
        Returns:
            True if sync should be scheduled
        """
        # Get checkpoint for this entity type
        result = await db.execute(
            select(SyncCheckpoint).where(
                and_(
                    SyncCheckpoint.store_id == store.id,
                    SyncCheckpoint.entity_type == entity_type.value
                )
            )
        )
        
        checkpoint = result.scalar_one_or_none()
        
        if not checkpoint:
            # No checkpoint exists, schedule sync
            return True
        
        # Check if enough time has passed since last sync
        if not checkpoint.last_successful_sync_at:
            # Never successfully synced, schedule sync
            return True
        
        # Get sync frequency for this entity type
        frequency_minutes = self._get_sync_frequency_minutes(entity_type)
        
        # Calculate time since last sync
        time_since_last_sync = datetime.utcnow() - checkpoint.last_successful_sync_at
        
        # Schedule if enough time has passed
        return time_since_last_sync >= timedelta(minutes=frequency_minutes)

    async def _should_schedule_reconciliation(
        self,
        db: AsyncSession,
        store: Store,
        entity_type: SyncEntityType
    ) -> bool:
        """
        Determine if a reconciliation sync should be scheduled.
        
        Args:
            db: Database session
            store: Store instance
            entity_type: Entity type to check
            
        Returns:
            True if reconciliation should be scheduled
        """
        # Get checkpoint
        result = await db.execute(
            select(SyncCheckpoint).where(
                and_(
                    SyncCheckpoint.store_id == store.id,
                    SyncCheckpoint.entity_type == entity_type.value
                )
            )
        )
        
        checkpoint = result.scalar_one_or_none()
        
        if not checkpoint:
            # No checkpoint, schedule reconciliation
            return True
        
        # Check if reconciliation is overdue
        reconciliation_frequency_hours = self._get_reconciliation_frequency_hours(entity_type)
        
        if checkpoint.last_successful_sync_at:
            time_since_last_sync = datetime.utcnow() - checkpoint.last_successful_sync_at
            return time_since_last_sync >= timedelta(hours=reconciliation_frequency_hours)
        
        return True

    async def _schedule_sync_job(
        self,
        store: Store,
        entity_type: SyncEntityType,
        sync_mode: SyncMode,
        priority: TaskPriority
    ) -> str:
        """
        Schedule a sync job using the queue service.
        
        Args:
            store: Store instance
            entity_type: Entity type to sync
            sync_mode: Sync mode
            priority: Task priority
            
        Returns:
            Celery task ID
        """
        job_data = {
            "store_id": store.id,
            "shop_domain": store.shop_domain,
            "access_token": store.admin_access_token,
            "entity_type": entity_type.value,
            "sync_mode": sync_mode.value,
            "batch_size": 50,
            "scheduled": True,
            "created_at": datetime.utcnow().isoformat()
        }
        
        task_id = celery_service.enqueue_sync_job(
            job_data=job_data,
            priority=priority
        )
        
        return task_id

    def _get_sync_frequency_minutes(self, entity_type: SyncEntityType) -> int:
        """Get sync frequency in minutes for entity type."""
        frequency_map = {
            SyncEntityType.PRODUCTS: 30,      # Every 30 minutes
            SyncEntityType.ORDERS: 15,        # Every 15 minutes
            SyncEntityType.CUSTOMERS: 60,     # Every hour
            SyncEntityType.COLLECTIONS: 120,  # Every 2 hours
            SyncEntityType.INVENTORY: 5       # Every 5 minutes
        }
        
        return frequency_map.get(entity_type, 60)

    def _get_reconciliation_frequency_hours(self, entity_type: SyncEntityType) -> int:
        """Get reconciliation frequency in hours for entity type."""
        frequency_map = {
            SyncEntityType.PRODUCTS: 24,      # Daily
            SyncEntityType.ORDERS: 24,        # Daily
            SyncEntityType.CUSTOMERS: 168,    # Weekly
            SyncEntityType.COLLECTIONS: 168,  # Weekly
            SyncEntityType.INVENTORY: 24      # Daily
        }
        
        return frequency_map.get(entity_type, 24)


# Global scheduler instance
sync_scheduler = SyncScheduler()
